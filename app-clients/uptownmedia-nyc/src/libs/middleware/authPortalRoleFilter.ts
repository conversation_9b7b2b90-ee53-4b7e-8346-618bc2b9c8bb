// import { UserRole } from "@/app/api/users/dao/user";
// const mapRedirect: Record<UserRole, string> = {
//   [UserRole.GUEST]: "/",
//   [UserRole.ADMIN]: "/portal/admin",
//   [UserRole.EDITOR]: "/portal/editor",
//   [UserRole.AUTHOR]: "/portal/author",
//   [UserRole.READER]: "/",
// };
//
// export const authPortalRoleFilter = (req, res) => {
//   const pathname = req.nextUrl.pathname;
//   const user = req.auth?.user;
//   const role: UserRole = user?.role;
//
//   console.log("User role:", role);
//   console.log("Current pathname:", pathname);
//   console.log("mapRedirect", mapRedirect[role]);
//
//   if (pathname.includes("portal") && pathname !== mapRedirect[role]) {
//     const user = req.auth?.user;
//     const role: UserRole = user?.role;
//
//     console.log("User role:", role);
//
//     if (!user)
//       return Response.redirect(new URL("/auth/sign-in", req.nextUrl.origin));
//
//     if (role) {
//       return Response.redirect(new URL(mapRedirect[role], req.nextUrl.origin));
//     }
//   }
// };
