import type { NextAuthRequest } from "next-auth";

import { type NextRequest, NextResponse } from "next/server";

import { UserRole } from "@/app/api/auth/users/dao/user";

const mapRedirect: Record<UserRole, string> = {
	[UserRole.GUEST]: "/",
	[UserRole.ADMIN]: "/portal/admin",
	[UserRole.EDITOR]: "/portal/editor",
	[UserRole.AUTHOR]: "/portal/author",
	[UserRole.READER]: "/",
};

const restrictedRoles = [UserRole.GUEST, UserRole.READER];
const protectedPaths = Object.values(mapRedirect).filter(
	(path) => !["/", "/auth/sign-in"].includes(path),
);

export const nextAuthFilter = (
	req: NextRequest,
	_: NextResponse | null,
): NextResponse | null => {
	const request = req as NextAuthRequest;
	const pathname = req.nextUrl.pathname;
	const user = request.auth?.user;
	const roles: UserRole[] | undefined = user?.roles;

	if (!roles || roles.length === 0) {
		return NextResponse.redirect(new URL("/auth/sign-in", req.nextUrl.origin));
	}

	const hasAllowedRole = roles.some((role) => !restrictedRoles.includes(role));

	if (!hasAllowedRole) {
		if (pathname === "/" || pathname.startsWith("/auth/sign-in")) {
			return NextResponse.next();
		}

		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	const isProtectedPath = protectedPaths.some((path) =>
		pathname.startsWith(path),
	);

	if (isProtectedPath) {
		const allowedRoles = roles.filter(
			(role) => !restrictedRoles.includes(role),
		);
		const allowedPaths = allowedRoles
			.map((role) => mapRedirect[role])
			.filter(Boolean);

		const isAllowed = allowedPaths.some((path) => pathname.startsWith(path));

		if (!isAllowed) {
			return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
		}
	}

	return NextResponse.next();
};
