"use client";
// import MultiPanel from "@/components/shared/sandbox/SecondaryContentPanelLayout";
// import { getVersionInfo } from "@/utils/getVersionInfo";

import usePortalStore, { setUserPreferences } from "@/stores/usePortalStore";
// import { useDisclosure } from "@heroui/react";
import { cn } from "@heroui/theme";
import { useTheme } from "next-themes";
import Image, { type StaticImageData } from "next/image";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import type React from "react";
import {
	type FC,
	type ReactNode,
	Suspense,
	useCallback,
	useEffect,
	useLayoutEffect,
	useMemo,
	useState,
} from "react";

import { useUrlTabs } from "@/hooks/useUrlTabs";
import { getUserPreferences } from "@/services/userService";
import { useUser } from "@/stores/state-handlers/useUser";
import { useUpdateUserPreferences } from "@/stores/state-handlers/useUserPreferences";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";
import useMediaQuery from "@/utils/useMediaQueries";
import { useDisclosure } from "@heroui/react";
import { InfoModal } from "../shared/info-modal";
import type { NavbarMenuItem } from "../shared/navbar/NavBarMenuItem";
import PortalNavbar from "../shared/navbar/PortalNavbar";
import { Sidebar } from "../shared/sidebar";
import type {
	SidebarItem,
	SidebarItemWithTranslation,
} from "../shared/sidebar/SidebarListbox";
import SidebarWrapper from "../shared/sidebar/SidebarWrapper";

import logoForLightMode from "@/assets/logo-black.png";
import logoForDarkMode from "@/assets/logo-white.png";

export type SidebarMode = "hidden" | "compact" | "expanded";
const validAccountSettingsTabs = [
	"profile",
	"no-allowed",
	"email",
	"family",
	"team",
	"credentials",
	"preferences",
	"notifications",
	"privacy",
	"terms",
];

export interface PortalLayoutProps {
	children: ReactNode;
	role?: string;
	sideBarItems: SidebarItemWithTranslation[];
	navbarMenuItems: NavbarMenuItem[];
}

const PortalLayout: FC<PortalLayoutProps> = ({
	children,
	role,
	sideBarItems,
	navbarMenuItems,
}) => {
	const router = useRouter();
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const modeParam = searchParams.get("mode") || "0";
	const { user, isLoading: isLoadingUser } = useUser();
	const { isMobile, isTablet, isDesktop } = useMediaQuery();
	// const navbarMenuItems: NavbarMenuItem[] = [];

	// const subscribeToThemeChanges = usePortalStore((state) => state.subscribeToThemeChanges);
	// const subscribeToLanguageChanges = usePortalStore((state) => state.subscribeToLanguageChanges);
	const userRole = usePortalStore((state) => state.userRole);
	const previouslyUserRole = usePortalStore(
		(state) => state.previouslyUserRole,
	);
	const { userPreferences, updateUserPreferences } = useUpdateUserPreferences();

	const { currentLanguage } = useLanguage();
	const { t: st } = useTranslation(currentLanguage, "sidebar");
	// const { t: nb } = useTranslation(currentLanguage, "navbar");

	const [viewportWidth, setViewportWidth] = useState<number>(
		typeof window !== "undefined" ? window.innerWidth : 1920,
	);

	const [compactMode, setCompactMode] = useState<boolean>(false);
	const [isOverlayMode, setIsOverlayMode] = useState<boolean>(false);
	const {
		isOpen: isAccountSettingsOpen,
		onOpen: onAccountSettingsModalOpen,
		onClose: onAccountSettingsModalClose,
	} = useDisclosure();
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

	const { theme } = useTheme();
	const [secondaryOpen, setSecondaryOpen] = useState<boolean>(false);

	const isNarrow = useMemo(() => viewportWidth <= 1920, [viewportWidth]);
	const logoLink = useMemo(() => {
		const rolePathArg = pathname.split("/")[2];
		if (rolePathArg !== "settings") return `/portal/${userRole}`;
		if (rolePathArg === "settings") return `/portal/${previouslyUserRole}`;

		return "/";
	}, [pathname, userRole, previouslyUserRole]);
	// Decide the width if `mode=1` (393px) or `mode=2` (786px), else 0
	const defaultSecondaryWidth = useMemo(() => {
		let width = 0;
		if (modeParam === "1") {
			width = 330;
			if (viewportWidth < 330) width = viewportWidth;
		} else if (modeParam === "2") {
			width = 660;
			if (viewportWidth < 660) width = viewportWidth;
		}
		return width;
	}, [modeParam, viewportWidth]);

	/*
	 * useLayoutEffect is trigger after windows loaded
	 * this ensures that the viewportWidth is set correctly
	 */
	useLayoutEffect(() => {
		const handleResize = () => setViewportWidth(window.innerWidth);
		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	useEffect(() => {
		setSecondaryOpen(defaultSecondaryWidth > 0);
	}, [defaultSecondaryWidth]);

	// useEffect(() => {
	//   reset &&
	//     getVersionInfo().then((versionInfo) => {
	//       const { uiVersion } = versionInfo;
	//       !uiVersion ||
	//         !UIVersion ||
	//         (uiVersion !== UIVersion && setUIVersion?.(uiVersion));
	//     });
	// }, [setUIVersion, UIVersion, reset]);

	const showSecondaryOverlay = useMemo(
		() => isNarrow || isMobile,
		[isNarrow, isMobile],
	);
	const pageLogo: ReactNode = useMemo(() => {
		return (
			<Image
				src={theme && theme === "dark" ? logoForDarkMode : logoForLightMode}
				alt="logo"
				width={160}
			/>
		);
	}, [theme]);

	const sideBarItemsTranslated = useMemo(() => {
		return sideBarItems?.map((item) => {
			const { titleTranslationKey, ...newItem } = {
				...item,
				title: st(item.titleTranslationKey),
				items: item.items?.map((subItem) => {
					const { titleTranslationKey, ...newSubItem } = {
						...subItem,
						title: st(subItem.titleTranslationKey),
						items: subItem.items?.map((subSubItem) => {
							const { titleTranslationKey, ...newSubSubItem } = {
								...subSubItem,
								title: st(subSubItem.titleTranslationKey),
							};
							return newSubSubItem;
						}),
					};
					return newSubItem;
				}),
			};
			return newItem;
		}) as SidebarItem[];
	}, [sideBarItems, st]);

	// used to know which sidebar menu item is selected
	const defaultSelectedKey = useMemo(() => {
		if (!sideBarItemsTranslated) return "dashboard";

		const flattenSidebarItems = (items: SidebarItem[]): SidebarItem[] =>
			items.flatMap((item) => [
				item,
				...(item.items ? flattenSidebarItems(item.items) : []),
			]);

		const flattenedSidebarMenu = flattenSidebarItems(sideBarItemsTranslated);
		const sbItem = flattenedSidebarMenu.find((item) => item.href === pathname);
		return sbItem?.key || "dashboard";
	}, [pathname, sideBarItemsTranslated]);

	const navbarMenuForRole = useMemo(() => {
		return navbarMenuItems?.filter(
			(item) => userRole && item.allowedWorkspaces?.includes(userRole),
		);
	}, [userRole, navbarMenuItems]);

	// const translatedSidebarItems = useMemo(() => {
	//   return sideBarItems.map((item) => {
	//     const { titleTranslationKey, ...rest } = item;
	//     return {
	//       ...rest,
	//       titleTranslationKey,
	//       title: st(titleTranslationKey),
	//       items: item.items?.map((subItem) => {
	//         const { titleTranslationKey: subKey, ...subRest } = subItem;
	//         return {
	//           ...subRest,
	//           titleTranslationKey: subKey,
	//           title: st(subKey),
	//         };
	//       }),
	//     };
	//   });
	// }, [sideBarItems, st]);

	// const translatedNavbarMenuItems = useMemo(() => {
	//   return navbarMenuItems.map((item) => {
	//     const { labelTKey, ...rest } = item;
	//     return {
	//       ...rest,
	//       label: nb(labelTKey),
	//     };
	//   });
	// }, [navbarMenuItems, nb]);

	useEffect(() => {
		if (modeParam !== undefined) {
			setSecondaryOpen(true);
		} else {
			setSecondaryOpen(false);
		}
	}, [modeParam]);

	useEffect(() => {
		if (user && !isLoadingUser) {
			getUserPreferences().then((res) => {
				setUserPreferences(res);
			});
		}
	}, [user, isLoadingUser]);

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useLayoutEffect(() => {
		const {
			sidebar: { compact },
		} = userPreferences;
		if (isDesktop) {
			setCompactMode(!!compact);
			setIsOverlayMode(false);
			onClose();
		} else if (isMobile) {
			setCompactMode(false);
			setIsOverlayMode(true);
		} else if (isTablet) {
			setCompactMode(true);
			setIsOverlayMode(false);
		} else if (isOverlayMode) {
			setCompactMode(false);
		}
	}, [isMobile, isTablet, isOverlayMode, isDesktop, onClose]);

	// useEffect(() => {
	//   subscribeToThemeChanges?.();
	//   subscribeToLanguageChanges?.();
	// }, [subscribeToThemeChanges, subscribeToLanguageChanges]);

	const handleSidebarActionButtonClick = useCallback(() => {
		if (isMobile) {
			onClose();
		} else if (!isMobile && isOverlayMode) {
			onClose();
			toggleOverlayMode();
		} else {
			toggleCompactMode();
		}
	}, [isMobile, isOverlayMode, onClose]);

	const {
		selectedTab: selectedAccountSettingsTab,
		setSelectedTab: setSelectedAccountSettingsTab,
		removeTabParam: clearAccountSettingsParameters,
	} = useUrlTabs({
		paramName: "account-settings",
		validTabs: validAccountSettingsTabs,
	});

	useEffect(() => {
		const shouldBeOpen =
			!!selectedAccountSettingsTab &&
			validAccountSettingsTabs.includes(selectedAccountSettingsTab);
		if (!isAccountSettingsOpen && shouldBeOpen) {
			onAccountSettingsModalOpen();
		} else if (isAccountSettingsOpen && !shouldBeOpen) {
			onAccountSettingsModalClose();
		}
	}, [
		selectedAccountSettingsTab,
		isAccountSettingsOpen,
		onAccountSettingsModalOpen,
		onAccountSettingsModalClose,
	]);

	const removeSomeQueryParams = (keys: string[]) => {
		const params = new URLSearchParams(searchParams.toString());
		for (const key of keys) {
			params.delete(key);
		}
		const newQuery = params.toString();
		const newUrl = newQuery ? `${pathname}?${newQuery}` : pathname;
		router.push(newUrl);
	};

	const closeSecondaryContent = () => {
		removeSomeQueryParams(["mode", "details"]);
		setSecondaryOpen(false);
	};

	function toggleOverlayMode() {
		const to = setTimeout(() => {
			if (compactMode) setCompactMode(false);
			setIsOverlayMode((prev) => !prev);
			clearTimeout(to);
		}, 50);
	}

	function toggleCompactMode() {
		if (!isOverlayMode || isTablet) {
			const to = setTimeout(() => {
				setCompactMode((prev) => !prev);

				updateUserPreferences({
					...userPreferences,
					sidebar: {
						...userPreferences.sidebar,
						compact: !compactMode,
					},
				}).then((res) => {
					if (res.updated) {
						console.log("updated user preferences", res.userPreferences);
					} else {
						console.log("no changes to user preferences", res.reason);
					}
				});
				clearTimeout(to);
			}, 50);
		}
	}
	return (
		<div className="w-screen h-screen flex justify-center relative">
			<div className="max-w-[2560px] w-full h-full relative flex flex-1">
				<SidebarWrapper
					compactMode={compactMode}
					hideCloseButton={true}
					isOpen={isOpen}
					isOverlayMode={isOverlayMode}
					sidebarWidth={262}
					onOpenChange={onOpenChange}
					className="max-w-[16.375rem] w-auto"
				>
					<Sidebar
						logo={pageLogo}
						logoLink={logoLink}
						compactMode={compactMode}
						defaultSelectedKey={defaultSelectedKey}
						isOverlayMode={isOverlayMode}
						items={sideBarItemsTranslated}
						toggleCompactMode={
							!isTablet ? handleSidebarActionButtonClick : onOpenChange
						}
						// toggleCompactMode={handleSidebarActionButtonClick}
					/>
				</SidebarWrapper>
				<div className="flex flex-col flex-1">
					<header className="w-full bg-background flex justify-between items-center">
						<PortalNavbar
							menu={navbarMenuForRole}
							menuPosition="end"
							isMobile={isMobile}
							triggerSidebar={onOpenChange}
						/>
					</header>

					<div className="flex flex-1 relative">
						{/* MAIN CONTENT */}
						<main className="flex-1 bg-background p-8">{children}</main>

						{/* SECONDARY CONTENT (MultiPanel) */}
						{/* {secondaryOpen &&
              defaultSecondaryWidth > 0 &&
              (showSecondaryOverlay ? (
                <aside
                  className={cn(
                    "absolute left-1 lg:left-auto xl:left-auto right-1 top-1 bottom-1 z-40 shadow-lg bg-transparent",
                    { "left-0 _w-full ": isMobile },
                  )}
                  style={{ width: (!isMobile && defaultSecondaryWidth) || "" }}
                >
                  <div className="flex justify-end">
                    <RoundedCloseButton onClick={closeSecondaryContent} />
                  </div>
                  <div className="relative flex h-full w-full">
                    <Suspense fallback={<div>Loading...</div>}>
                      <MultiPanel onClosePanel={closeSecondaryContent} />
                    </Suspense>
                  </div>
                </aside>
              ) : (
                <aside
                  className="bg-gray-100 p-4"
                  style={{ width: (!isMobile && defaultSecondaryWidth) || "" }}
                >
                  <div className="flex justify-end">
                    <RoundedCloseButton onClick={closeSecondaryContent} />
                  </div>
                  <Suspense fallback={<div>Loading...</div>}>
                    <MultiPanel onClosePanel={closeSecondaryContent} />
                  </Suspense>
                </aside>
              ))} */}
					</div>
				</div>
			</div>
			{isAccountSettingsOpen && (
				<InfoModal
					selectedTab={selectedAccountSettingsTab}
					setSelectedTab={setSelectedAccountSettingsTab}
					removeTabParam={clearAccountSettingsParameters}
					isOpen={isAccountSettingsOpen}
					onClose={clearAccountSettingsParameters} //onAccountSettingsModalClose}
					onOpen={onAccountSettingsModalOpen}
				/>
			)}
		</div>
	);
};

export default PortalLayout;
