"use client";
import useCustomHover from "@/hooks/useCustomHover";
import type { User as UserType } from "@/types";
import { Avatar } from "@heroui/react";
import { cn } from "@heroui/theme";
import { useParams } from "next/navigation";
import type React from "react";

interface UserInfoProps {
	user?: UserType;
	isMobile: boolean;
	loadingUser: boolean;
	hovered?: boolean;
}

const UserInfo: React.FC<UserInfoProps> = ({
	user,
	isMobile,
	loadingUser,
	hovered,
}) => {
	const { hoverProps, isHovered } = useCustomHover();
	const { role: selectedRole } = useParams<{ role: string }>();

	return (
		<div
			{...hoverProps}
			className={
				"flex gap-2 justify-between md:justify-content min-w-28 md:h-10 md:w-10 bg-transparent rounded-none px-0"
			}
		>
			{user?.image && (
				<Avatar
					className={cn("bg-neutral-200 flex-none min-h-auto ")}
					isBordered={false}
					showFallback={true}
					size={
						isMobile ? "md" : "md" // this could vary based on the design
					}
					src={user?.image}
				/>
			)}
			{!isMobile && (
				<div className={"flex flex-1"}>
					<div className="flex max-w-full flex-col text-left hover:text-primary focus:text-primary">
						{!loadingUser && (
							<>
								<span
									className={cn("text-tiny text-default-400", {
										"text-primary": hovered !== undefined ? hovered : isHovered,
									})}
								>
									{selectedRole} {/* t("roleNames.<selectedRole>") */}
								</span>
								<span
									className={cn("text-medium text-default-600 break-all", {
										"text-primary": hovered !== undefined ? hovered : isHovered,
									})}
								>
									{user?.name || user?.email}
								</span>
							</>
						)}
						{loadingUser && (
							<div className="flex flex-col gap-2 animate-pulse">
								<span className="text-default-400 h-4 w-28 bg-default-100 rounded-tiny" />
								<span className="font-medium text-default-600 h-6 w-24 bg-default-100 rounded-sm" />
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default UserInfo;
