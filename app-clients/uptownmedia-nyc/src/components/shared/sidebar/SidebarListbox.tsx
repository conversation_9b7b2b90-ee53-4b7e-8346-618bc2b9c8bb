"use client";

import {
	type ListboxProps,
	type ListboxSectionProps,
	type Selection,
	cn,
} from "@heroui/react";
import { Listbox, ListboxItem, ListboxSection, Tooltip } from "@heroui/react";
import { AnimatePresence, motion } from "framer-motion";
import React, { type ReactNode, useEffect, useMemo, useState } from "react";
import { Icon } from "../icons";
import type { IconName } from "../icons/interfaces";
import NestedItemMenu from "./NestedItemMenu";

export enum SidebarItemType {
	Nest = "nest",
}

export type SidebarItem = {
	key: string;
	title: string;
	icon?: IconName;
	href?: string;
	type?: SidebarItemType.Nest;
	startContent?: React.ReactNode;
	endContent?: React.ReactNode;
	items?: SidebarItem[];
	className?: string;
};

export type SidebarItemWithTranslation = Omit<
	SidebarItem,
	"title" | "items"
> & {
	title?: string;
	titleTranslationKey: string;
	items?: SidebarItemWithTranslation[];
};

export type SidebarProps = Omit<ListboxProps<SidebarItem>, "children"> & {
	items: SidebarItem[];
	isCompact?: boolean;
	hideEndContent?: boolean;
	iconClassName?: string;
	sectionClasses?: ListboxSectionProps["classNames"];
	classNames?: ListboxProps["classNames"];
	defaultSelectedKey: string;
	onSelect?: (key: string) => void;
};

const NestItem: React.FC<{
	item: SidebarItem;
	isCompact?: boolean;
	hideEndContent?: boolean;
	iconClassName?: string;
	renderItem: (item: SidebarItem) => React.ReactNode;
}> = ({ item, isCompact, hideEndContent, iconClassName, renderItem }) => {
	const isNestType =
		item.items && item.items?.length > 0 && item?.type === SidebarItemType.Nest;
	if (isNestType) {
		// Is a nest type item , so we need to remove the href
		// biome-ignore lint/performance/noDelete: <explanation>
		delete item.href;
	}

	return (
		<ListboxItem
			aria-label={item.title}
			{...item}
			key={item.key}
			className="gap-[10px] flex-col"
			classNames={{
				base: cn(
					"gap-[10px]",
					{
						"h-auto p-0": !isCompact && isNestType,
					},
					{
						"inline-block w-11": isCompact && isNestType,
					},
				),
				title: "group-data-[selected=true]:text-primary text-lg",
			}}
			endContent={
				isCompact || isNestType || hideEndContent
					? null
					: (item.endContent ?? null)
			}
			startContent={
				isCompact || isNestType ? null : item.icon ? (
					<Icon
						name={item.icon}
						className={cn(
							"text-default-500 group-data-[selected=true]:text-foreground",
							iconClassName,
						)}
					/>
				) : (
					(item.startContent ?? null)
				)
			}
			title={
				isCompact ? <span className="h-16" /> : isNestType ? null : item.title
			}
		>
			<NestedItemMenu
				item={item}
				renderItem={renderItem}
				iconClassName={iconClassName}
				className="p-0"
				isCompact={isCompact}
			/>
		</ListboxItem>
	);
};

const SidebarListbox = React.forwardRef<HTMLElement, SidebarProps>(
	(
		{
			items,
			isCompact,
			defaultSelectedKey,
			onSelect,
			hideEndContent,
			sectionClasses: sectionClassesProp = {},
			itemClasses: itemClassesProp = {},
			iconClassName,
			classNames,
			className,
			...props
		},
		ref,
	) => {
		const sectionClasses = useMemo(
			() => ({
				...sectionClassesProp,
				base: cn(sectionClassesProp?.base, "w-full py-2.5 mb-0", {
					"max-w-[56px]": isCompact,
				}),
				group: cn("flex flex-col gap-2", sectionClassesProp?.group, {
					"flex flex-col gap-2": isCompact,
				}),
				heading: cn("font-extralight font-sm", sectionClassesProp?.heading, {
					"hidden mb-6": isCompact,
				}),
				title: "text-lg",
			}),
			[isCompact, sectionClassesProp],
		);

		const itemClasses = useMemo(
			() => ({
				...itemClassesProp,
				base: cn(itemClassesProp?.base, {
					"w-11 h-11 gap-0 p-0": isCompact,
				}),
			}),
			[isCompact, itemClassesProp],
		);

		// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
		const renderItem = React.useCallback(
			(item: SidebarItem) => {
				const isNestType =
					item.items &&
					item.items?.length > 0 &&
					item?.type === SidebarItemType.Nest;

				if (isNestType) {
					return (
						<NestItem
							item={item}
							isCompact={isCompact}
							hideEndContent={hideEndContent}
							iconClassName={iconClassName}
							renderItem={renderItem}
						/>
					);
				}

				return (
					<ListboxItem
						{...item}
						key={item.key}
						endContent={
							isCompact || hideEndContent ? null : (item.endContent ?? null)
						}
						startContent={
							isCompact ? null : item.icon ? (
								<Icon
									className={cn(
										"group-data-[selected=true]:text-primary h-6 w-6",
										iconClassName,
									)}
									name={item.icon}
								/>
							) : (
								(item.startContent ?? null)
							)
						}
						textValue={item.title}
						title={isCompact ? null : item.title}
						classNames={{
							title: cn(
								"group-data-[selected=true]:text-primary text-md truncate",
							),
							base: "data-[hover=true]:bg-default-100 data-[selectable=true]:focus:bg-default-100 spy-2 group-data-[theme=dark]:hover:bg-default-100 group-data-[theme=dark]:hover:text-default-800",
						}}
					>
						{isCompact ? (
							<Tooltip
								content={item.title}
								placement="right"
								classNames={{ content: "truncate" }}
							>
								<div className="flex w-full items-center justify-center">
									{item.icon ? (
										<Icon
											className={cn(
												"group-data-[selected=true]:text-primary h-6 w-6",
												iconClassName,
											)}
											name={item.icon}
										/>
									) : (
										(item.startContent ?? null)
									)}
								</div>
							</Tooltip>
						) : null}
					</ListboxItem>
				);
			},
			[isCompact, hideEndContent, iconClassName, itemClasses?.base],
		);

		return (
			<Listbox
				key={isCompact ? "compact" : "default"}
				ref={ref}
				hideSelectedIcon
				as="nav"
				className={cn("list-none py-2ss", className)}
				classNames={{
					...classNames,
					list: cn("items-center", classNames?.list),
				}}
				color="default"
				itemClasses={{
					...itemClasses,
					base: cn(
						"px-0 py-0 min-h-11 rounded-large h-[56px] data-[selected=true]:bg-default-100",
						itemClasses?.base,
					),
					title: cn(
						"text-sm text-default-500 group-data-[selected=true]:text-foreground",
						itemClasses?.title,
					),
				}}
				items={items}
				selectedKeys={[defaultSelectedKey] as unknown as Selection}
				selectionMode="single"
				variant="flat"
				onSelectionChange={(keys) => {
					const key = Array.from(keys)[0];

					// setSelected(key as React.Key);
					onSelect?.(key as string);
				}}
				{...props}
			>
				{(item) => {
					return item.items &&
						item.items?.length > 0 &&
						item?.type === SidebarItemType.Nest ? (
						<NestItem
							item={item}
							isCompact={isCompact}
							hideEndContent={hideEndContent}
							iconClassName={iconClassName}
							renderItem={renderItem}
						/>
					) : item.items && item.items?.length > 0 ? (
						<ListboxSection
							key={item.key}
							classNames={{
								...sectionClasses,
								base: cn(sectionClasses?.base, {
									"border-b-[0.5px] border-b-default":
										item.key !== items[items.length - 1].key,
								}),
							}}
							title={item.title}
						>
							{item.items.map(renderItem)}
						</ListboxSection>
					) : (
						renderItem(item)
					);
				}}
			</Listbox>
		);
	},
);

SidebarListbox.displayName = "SidebarListbox";

export default SidebarListbox;
