"use client";

import type { UserRole } from "@/app/api/auth/users/dao/user";
// import useAppStore from '@/stores/students/useAppStore';
import Select, { selectItemClassName } from "@/components/ui/Select";
import usePortalStore, { setUserRole } from "@/stores/usePortalStore";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";
import {
	Button,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownSection,
	DropdownTrigger,
	SelectItem,
	SelectSection,
	Spacer,
	cn,
} from "@heroui/react";
import Link from "next/link";
import { redirect, useParams } from "next/navigation";
import { usePathname } from "next/navigation";
import React, { useEffect, useLayoutEffect, useMemo, useState } from "react";
import LoadingContent from "../ui/LoadingContent";
import { Icon } from "./icons";

type Workspace = {
	name: string;
	link: string;
	id: string;
};

type RoleToWorkspaceMap = Record<UserRole, Workspace>;
// user Roles: <PERSON><PERSON>, Candidate, Scholar, Staff, Counselor,  Admission Officer, School Rep

function WorkspacesSelector({
	isCompact,
}: { workspace?: string; isCompact?: boolean }) {
	const { role } = useParams<{ role: string }>();
	const userRole = usePortalStore((state) => state.userRole);
	const previouslyUserRole = usePortalStore(
		(state) => state.previouslyUserRole,
	);
	// const setUserRole = usePortalStore((state) => state.setSelectedWorkspace);
	const selectedOption = useMemo(
		() => (userRole !== undefined ? [userRole] : []),
		[userRole],
	);
	// const [selectedOption, setSelectedOption] = useState<string[]>(workspace !== undefined ? [workspace] : []);
	const [isLoading, setIsLoading] = useState(true);

	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "workspaces");
	const workspacesName = t("roles");
	const user = usePortalStore((state) => state.user);

	useLayoutEffect(() => {
		if ((role && role !== userRole) || !previouslyUserRole) {
			setUserRole(role as UserRole);
		}
		setIsLoading(false);
	}, [role, userRole, previouslyUserRole]);

	const roleToWorkspaceMap: RoleToWorkspaceMap = useMemo(
		() => ({
			editor: {
				id: "editor",
				name: t("roleNames.editor"),
				link: "/portal/editor/articles",
			},
			author: {
				id: "author",
				name: t("roleNames.author"),
				link: "/portal/author/articles",
			},
			admin: {
				id: "admin",
				name: t("roleNames.admin"),
				link: "/portal/admin",
			},
			guest: {
				id: "guest",
				name: t("roleNames.guest"),
				link: "/portal/guest",
			},
			reader: {
				id: "reader",
				name: t("roleNames.reader"),
				link: "/portal/reader",
			},
		}),
		[t],
	);

	const workspaces = useMemo(() => {
		return (
			user?.roles?.map((role) => roleToWorkspaceMap[role]).filter(Boolean) || []
		);
	}, [user?.roles, roleToWorkspaceMap]);

	function handleSelect(value: string) {
		setUserRole(value as UserRole);
		const workspace = workspaces.find((workspace) => workspace.id === value);
		workspace && redirect(workspace.link);
	}

	if (isLoading) {
		return (
			<div className="w-full flex flex-col gap-1 px-5">
				<Spacer y={2} className="h-0" />
				{!isCompact && <LoadingContent />}
				{isCompact && (
					<Button
						variant="bordered"
						className="min-w-0 px-2 py-3 border-thin rounded-lg border-foreground-900 h-auto animate-pulse"
						aria-placeholder={workspacesName}
					>
						<Icon name="objects-column" className="h-5 w-5" />
					</Button>
				)}
			</div>
		);
	}

	return (
		userRole && (
			<div className="w-full flex flex-col gap-1 px-5">
				<Spacer y={2} className="h-0" />
				{!isCompact && (
					<Select
						selectedKeys={selectedOption}
						classNames={{
							trigger: "min-w-0 px-4 py-3 group-data-[theme=dark]:bg-content1",
							value: "leading-tight",
						}}
						variant="bordered"
						onChange={(e) => handleSelect(e.target.value)}
						aria-label={workspacesName}
						selectorIcon={
							<Icon name="chevron-down" className="text-primary-700" />
						}
					>
						<SelectSection title={workspacesName}>
							{workspaces.map((workspace) => (
								<SelectItem
									key={workspace.id}
									classNames={{
										base: selectItemClassName,
										title: "text-medium",
									}}
								>
									{workspace.name}
								</SelectItem>
							))}
						</SelectSection>
					</Select>
				)}
				{isCompact && (
					<Dropdown placement="right-start">
						<DropdownTrigger>
							<Button
								variant="bordered"
								className="min-w-0 px-2 py-3 border rounded-lg border-foreground-900 h-auto"
								aria-placeholder={workspacesName}
							>
								<Icon name="objects-column" className="h-5 w-5" />
							</Button>
						</DropdownTrigger>
						{workspaces?.length > 0 && (
							<DropdownMenu
								aria-label="Static Actions"
								className="md:text-base"
								selectedKeys={selectedOption}
								onAction={(selected) => handleSelect(selected as string)}
								selectionMode="single"
							>
								<DropdownSection title={workspacesName}>
									{workspaces.map((workspace, idx) => (
										<DropdownItem
											key={workspace.id}
											as={Link}
											href={workspace.link}
											className={selectItemClassName}
										>
											{workspace.name}
										</DropdownItem>
									))}
								</DropdownSection>
							</DropdownMenu>
						)}
					</Dropdown>
				)}
			</div>
		)
	);
}

export default WorkspacesSelector;
