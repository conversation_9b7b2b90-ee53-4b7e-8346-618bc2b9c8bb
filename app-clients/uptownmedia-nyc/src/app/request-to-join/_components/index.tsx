"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { description, subtitle, title } from "@/components/primitives";
import uLogo from "../../../../public/assets/u-logo.png";

const formSchema = z.object({
	phoneNumber: z.string().optional(),
	inviteCode: z.string().min(1, { message: "Invite code is required" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function RequestToJoin() {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submitSuccess, setSubmitSuccess] = useState(false);
	const [errorMessage, setErrorMessage] = useState("");
	const [userName, setUserName] = useState("");
	const [isLoading, setIsLoading] = useState(true);

	const { data: session, status } = useSession({
		required: true,
		onUnauthenticated() {
			router.push("/auth/sign-in");
		},
	});

	const {
		register,
		handleSubmit,
		reset,
		formState: { errors },
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
	});

	useEffect(() => {
		if (status === "loading") {
			setIsLoading(true);
		} else {
			setIsLoading(false);
			if (status === "authenticated") {
				if (session?.user?.name) {
					setUserName(session.user.name);
				} else if (session?.user?.email) {
					setUserName(session.user.email.split("@")[0]);
				}
			}
		}
	}, [session, status]);

	const onSubmit = async (data: FormValues) => {
		setIsSubmitting(true);
		setErrorMessage("");

		try {
			// Prepare the API request data
			const requestData = {
				userId: session?.user?.id || session?.user?.email || "unknown",
				phoneNumber: data.phoneNumber || undefined,
			};

			// Call the group membership API endpoint
			const response = await fetch(`/api/groups/${data.inviteCode}/members`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestData),
			});

			if (response.ok) {
				console.log("Join request submitted successfully:", data);
				// Clear form data on successful submission
				reset();
				setSubmitSuccess(true);
			} else {
				const errorData = await response.json();
				setErrorMessage(
					errorData.error || errorData.message || "Failed to submit join request. Please check your invite code and try again.",
				);
			}
		} catch (error) {
			console.error("Error submitting join request:", error);
			setErrorMessage(
				"An error occurred while submitting your request. Please try again.",
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center w-full min-h-screen p-4">
				<div className="relative">
					<div className="absolute inset-0 z-50 flex items-center justify-center mx-auto bg-black bg-center rounded-full -top-7 w-14 h-14">
						<Link className="flex items-center justify-center" href="/">
							<Image
								alt="Uptown Media Logo"
								className="object-cover"
								src={uLogo}
							/>
						</Link>
					</div>
					<Card className="w-full max-w-[545px] shadow-lg">
						<CardBody className="flex items-center justify-center gap-4 p-10">
							<div className="text-center">
								<h2 className={subtitle({ size: "lg" })}>Loading...</h2>
								<p className={description({ size: "sm", className: "mt-2" })}>
									Please wait while we load your information.
								</p>
							</div>
						</CardBody>
					</Card>
				</div>
			</div>
		);
	}

	if (submitSuccess) {
		return (
			<div className="flex items-center justify-center w-full min-h-screen p-4">
				<div className="relative">
					<div className="absolute inset-0 z-50 flex items-center justify-center mx-auto bg-black bg-center rounded-full -top-7 w-14 h-14">
						<Link className="flex items-center justify-center" href="/">
							<Image
								alt="Uptown Media Logo"
								className="object-cover"
								src={uLogo}
							/>
						</Link>
					</div>
					<Card className="w-full max-w-[545px] shadow-lg">
						<CardBody className="gap-4 p-10">
							<div className="flex flex-col items-center">
								<div className="text-center">
									<h1 className={title({ size: "md", className: "font-bold" })}>
										Request to Join
									</h1>
									<p className={title({ size: "sm", className: "mt-2 mb-4" })}>
										Hello {userName}
									</p>
									<p className={description({ size: "sm", className: "mt-4" })}>
										Request has been submitted to
										<br />
										<span className="font-semibold uppercase">
											NAME OF EDITOR
										</span>
									</p>
									<p className={description({ size: "sm", className: "mt-4" })}>
										You will receive an email once access is granted
									</p>
								</div>
							</div>
						</CardBody>
					</Card>
				</div>
			</div>
		);
	}

	return (
		<div className="flex items-center justify-center w-full min-h-screen p-4">
			<div className="relative">
				<div className="absolute inset-0 z-50 flex items-center justify-center mx-auto bg-black bg-center rounded-full -top-7 w-14 h-14">
					<Link className="flex items-center justify-center" href="/">
						<Image
							alt="Uptown Media Logo"
							className="object-cover"
							src={uLogo}
						/>
					</Link>
				</div>
				<Card className="w-full max-w-3xl shadow-lg md:px-20">
					<CardBody className="gap-2 p-10">
						<h1 className={title({ size: "lg", className: "text-center" })}>
							Request to Join
						</h1>

						<p className={subtitle({ size: "sm", className: "text-center" })}>
							Hello {userName}
						</p>
						<p
							className={description({ size: "sm", className: "text-center" })}
						>
							Please provide the following required information
						</p>

						{errorMessage && (
							<div className="p-3 mb-4 text-sm text-white bg-red-500 rounded">
								{errorMessage}
							</div>
						)}

						<form
							onSubmit={handleSubmit(onSubmit)}
							className="flex flex-col gap-4"
						>
							<div className="flex flex-col">
								<label htmlFor="phoneNumber" className="mb-1 text-sm">
									Phone Number (Optional)
								</label>
								<input
									id="phoneNumber"
									type="tel"
									className={`p-1.5 border rounded ${errors.phoneNumber ? "border-red-500" : "border-gray-300"}`}
									placeholder="Phone Number"
									{...register("phoneNumber")}
								/>
								{errors.phoneNumber && (
									<span className="mt-1 text-xs text-red-500">
										{errors.phoneNumber.message}
									</span>
								)}
							</div>

							<div className="flex flex-col">
								<label htmlFor="inviteCode" className="mb-1 text-sm">
									Invite Code <span className="text-red-500">*</span>
								</label>
								<input
									id="inviteCode"
									type="text"
									className={`p-1.5 border rounded ${errors.inviteCode ? "border-red-500" : "border-gray-300"}`}
									placeholder="Invite Code"
									{...register("inviteCode")}
								/>
								{errors.inviteCode && (
									<span className="mt-1 text-xs text-red-500">
										{errors.inviteCode.message}
									</span>
								)}
							</div>

							<Button
								type="submit"
								className="w-full mt-4 text-white bg-black"
								isLoading={isSubmitting}
								isDisabled={isSubmitting}
							>
								Submit Request
							</Button>
						</form>
					</CardBody>
				</Card>
			</div>
		</div>
	);
}
