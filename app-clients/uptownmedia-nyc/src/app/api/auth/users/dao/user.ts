// Definición de los roles posibles para los usuarios
export enum UserRole {
	ADMIN = "admin",
	AUTHOR = "author",
	EDITOR = "editor",
	GUEST = "guest",
	READER = "reader",
}

export class User {
	private userId: string; // UUIDv7
	private tenantId: string;
	private name: string;
	private password: string;
	private email: string;
	private phone?: string;
	private rolesIds: string[]; // UUIDv7
	private socialProvider: string;
	private failedAttemptCount: number;
	private lockoutExpiresAt?: number;
	// biome-ignore lint/suspicious/noExplicitAny: atributos flexibles definidos por el usuario
	private attributes: Record<string, any>;

	constructor(
		userId: string,
		tenantId: string,
		name: string,
		password: string,
		email: string,
		rolesIds: string[],
		socialProvider: string,
		failedAttemptCount = 0,
		// biome-ignore lint/suspicious/noExplicitAny: atributos flexibles definidos por el usuario
		attributes: Record<string, any> = {},
		phone?: string,
		lockoutExpiresAt?: number,
	) {
		this.userId = userId;
		this.tenantId = tenantId;
		this.name = name;
		this.password = password;
		this.email = email;
		this.phone = phone;
		this.rolesIds = rolesIds;
		this.socialProvider = socialProvider;
		this.failedAttemptCount = failedAttemptCount;
		this.lockoutExpiresAt = lockoutExpiresAt;
		this.attributes = attributes;
	}

	getUserId(): string {
		return this.userId;
	}
	setUserId(userId: string): void {
		this.userId = userId;
	}

	getTenantId(): string {
		return this.tenantId;
	}
	setTenantId(tenantId: string): void {
		this.tenantId = tenantId;
	}

	getName(): string {
		return this.name;
	}
	setName(name: string): void {
		this.name = name;
	}

	getEmail(): string {
		return this.email;
	}
	setEmail(email: string): void {
		this.email = email;
	}

	getPassword(): string {
		return this.password;
	}

	setPassword(password: string): void {
		this.password = password;
	}

	getPhone(): string | undefined {
		return this.phone;
	}
	setPhone(phone: string): void {
		this.phone = phone;
	}

	getRolesIds(): string[] {
		return this.rolesIds;
	}
	setRoleId(rolesIds: string[]): void {
		this.rolesIds = rolesIds;
	}

	getSocialProvider(): string {
		return this.socialProvider;
	}
	setSocialProvider(socialProvider: string): void {
		this.socialProvider = socialProvider;
	}

	getFailedAttemptCount(): number {
		return this.failedAttemptCount;
	}
	setFailedAttemptCount(count: number): void {
		this.failedAttemptCount = count;
	}

	getLockoutExpiresAt(): number | undefined {
		return this.lockoutExpiresAt;
	}
	setLockoutExpiresAt(timestamp: number): void {
		this.lockoutExpiresAt = timestamp;
	}

	// biome-ignore lint/suspicious/noExplicitAny: atributos flexibles definidos por el usuario
	getAttributes(): Record<string, any> {
		return this.attributes;
	}
	// biome-ignore lint/suspicious/noExplicitAny: atributos flexibles definidos por el usuario
	setAttributes(attributes: Record<string, any>): void {
		this.attributes = attributes;
	}
	toSafeObject() {
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		const { password, ...safeData } = this as any; // o usa un DTO si prefieres

		return safeData;
	}
}
