import PortalLayout from "@/components/layout/PortalLayout";
import type { SidebarItemWithTranslation } from "@/components/shared/sidebar/SidebarListbox";
import { navbarMenuItems } from "@/utils/portalNavbarMenuItems";
import React, { type ReactNode } from "react";

const sidebarItemsGroups: { [key: string]: SidebarItemWithTranslation[] } = {
	editor: [
		{
			key: "articles",
			title: "Articles",
			titleTranslationKey: "editor.articles",
			href: "/portal/editor/articles",
			icon: "book-open-reader",
		},
		{
			key: "publisher",
			title: "Publisher",
			titleTranslationKey: "editor.publisher",
			href: "/portal/editor/publisher",
			icon: "check",
		},
	],
	author: [
		{
			key: "articles",
			title: "Articles",
			titleTranslationKey: "author.articles",
			href: "/portal/author/articles",
			icon: "book-open-reader",
		},
	],
};

async function Layout({
	children,
	params,
}: {
	children: ReactNode;
	params: Promise<{ role: "editor" | "author" | "guest" }>;
}) {
	const { role } = await params;
	const sidebarMenuItems =
		role === "editor" || role === "author" ? sidebarItemsGroups[role] : [];
	return (
		<PortalLayout
			role={role}
			sideBarItems={sidebarMenuItems}
			navbarMenuItems={navbarMenuItems}
		>
			{children}
		</PortalLayout>
	);
}

export default Layout;
