import type { User } from "@/types";
import { getHighPriorityRole } from "@/types/rolesPriority";
import { compareObjects } from "@/utils/compareObjects";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import usePortalStore, { setUser, setUserRole } from "../usePortalStore";

export function useUser() {
	const [isLoading, setIsLoading] = useState(false);
	const user = usePortalStore((state) => state.user);
	const session = useSession();

	useEffect(() => {
		if (session.data) {
			const { user: sessionUser } = session.data;
			const sameinfo =
				(user && sessionUser && compareObjects(user, sessionUser)) || false;
			if (!sameinfo) {
				setUser(sessionUser as User);
				const higherRole =
					sessionUser?.roles && getHighPriorityRole(sessionUser.roles);
				if (higherRole) {
					setUserRole(higherRole);
				}
			}
			console.log("sameinfo", sameinfo);
		}
	}, [session, user]);

	return { user, isLoading };
}
